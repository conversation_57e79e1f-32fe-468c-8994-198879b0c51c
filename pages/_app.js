import PropTypes from 'prop-types'
import { useEffect } from 'react'

import { appWithTranslation } from 'next-i18next'
import { QueryClient, QueryClientProvider } from 'react-query'
import { PageLoadingProvider } from 'ui/feedback/PageLoading'
import OfflineBanner from '../components/OfflineBanner'

import 'styles/styles.css'
import 'styles/fonts.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: true,
    },
  },
})

function BaseFrontend({ Component, pageProps }) {
  // Global error handler for offline router issues
  useEffect(() => {
    const handleGlobalError = event => {
      const error = event.error || event.reason

      // Check if this is a router navigation error when offline
      if (
        error?.message?.includes('navigate') &&
        typeof navigator !== 'undefined' &&
        !navigator.onLine
      ) {
        // Prevent the error from showing in the console for offline navigation issues
        event.preventDefault()
        // eslint-disable-next-line no-console
        console.warn(
          'Router navigation failed while offline (this is expected):',
          error.message
        )
      }
    }

    const handleUnhandledRejection = event => {
      const error = event.reason

      // Check if this is a router navigation error when offline
      if (
        error?.message?.includes('navigate') &&
        typeof navigator !== 'undefined' &&
        !navigator.onLine
      ) {
        // Prevent the unhandled rejection from showing for offline navigation issues
        event.preventDefault()
        // eslint-disable-next-line no-console
        console.warn(
          'Router navigation promise rejected while offline (this is expected):',
          error.message
        )
      }
    }

    // Add global error handlers
    window.addEventListener('error', handleGlobalError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleGlobalError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <PageLoadingProvider>
        <OfflineBanner />
        <Component {...pageProps} />
      </PageLoadingProvider>
    </QueryClientProvider>
  )
}
BaseFrontend.propTypes = {
  Component: PropTypes.func,
  pageProps: PropTypes.object,
}

export default appWithTranslation(BaseFrontend)
