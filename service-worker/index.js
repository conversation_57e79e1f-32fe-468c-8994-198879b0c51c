import { defaultCache } from '@serwist/next/worker'
import {
  <PERSON>wi<PERSON>,
  NetworkFirst,
  CacheFirst,
  StaleWhileRevalidate,
} from 'serwist'

const OFFLINE_FALLBACK_PAGE = '/offline.html'

const serwist = new Serwist({
  precacheEntries: self.__SW_MANIFEST || [],
  precacheOptions: {
    cleanupOutdatedCaches: true,
    concurrency: 10,
    fallbackToNetwork: true,
    ignoreURLParametersMatching: [],
  },
  skipWaiting: false, // Prevent immediate activation that can cause reload loops
  clientsClaim: false, // Prevent immediate control that can cause reload loops
  navigationPreload: true,
  fallbacks: {
    // Make sure the offline page is precached
    document: OFFLINE_FALLBACK_PAGE,
  },
  runtimeCaching: [
    // General navigation requests with longer timeout to prevent premature fallbacks
    {
      matcher: ({ request }) => request.mode === 'navigate',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 10, // Increased timeout to prevent premature fallbacks
        cacheName: 'pages-cache',
        plugins: [
          {
            handlerDidError: async ({ url }) => {
              console.log('Navigation request failed for:', url.pathname)

              // Special handling for downloads page
              if (url.pathname === '/downloads') {
                // Try to serve the dedicated offline downloads page
                try {
                  const offlineDownloadsResponse = await caches.match(
                    '/downloads-offline.html'
                  )
                  if (offlineDownloadsResponse) {
                    console.log('Serving offline downloads page')
                    return offlineDownloadsResponse
                  }
                } catch (error) {
                  console.warn('Could not serve offline downloads page:', error)
                }
              }

              // General fallback to main offline page
              try {
                const cachedResponse = await caches.match(OFFLINE_FALLBACK_PAGE)
                if (cachedResponse) {
                  console.log('Serving general offline page')
                  return cachedResponse
                }
              } catch (error) {
                console.warn('Could not serve offline page from cache:', error)
              }

              // Final fallback - simple offline message
              return new Response(
                `
                <!DOCTYPE html>
                <html>
                <head><title>Offline</title></head>
                <body>
                  <h1>You are offline</h1>
                  <p>Please check your internet connection and try again.</p>
                  <button onclick="window.location.reload()">Retry</button>
                </body>
                </html>
                `,
                {
                  status: 503,
                  headers: { 'Content-Type': 'text/html' },
                }
              )
            },
          },
        ],
      }),
    },
    // Enhanced caching for JavaScript chunks and dynamic imports
    {
      matcher: ({ url }) => url.pathname.includes('/_next/static/chunks/'),
      handler: new CacheFirst({
        cacheName: 'next-js-chunks',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              // Only cache successful responses
              return response.status === 200 ? response : null
            },
            // Add error handling to prevent cache corruption
            handlerDidError: async () => {
              console.log('Failed to load JS chunk, falling back to network')
              return null // Let it fall back to network
            },
          },
        ],
      }),
    },
    // Use StaleWhileRevalidate for CSS and static assets to prevent reload loops
    {
      matcher: ({ url }) =>
        url.pathname.includes('/_next/static/css/') ||
        url.pathname.endsWith('.css'),
      handler: new StaleWhileRevalidate({
        cacheName: 'css-assets',
        plugins: [
          {
            cacheWillUpdate: async ({ response }) => {
              return response.status === 200 ? response : null
            },
            // Prevent CSS loading errors from causing page reloads
            handlerDidError: async () => {
              console.log('CSS loading failed, continuing without styles')
              return new Response('/* CSS loading failed */', {
                status: 200,
                headers: { 'Content-Type': 'text/css' },
              })
            },
          },
        ],
      }),
    },
    ...defaultCache,
  ],
})

// Add debugging and safety measures for Chrome compatibility
self.addEventListener('install', () => {
  console.log('Service worker installing...')
  // Don't skip waiting to prevent reload loops
  // self.skipWaiting() is disabled above
})

self.addEventListener('activate', () => {
  console.log('Service worker activating...')
  // Don't claim clients immediately to prevent reload loops
  // self.clients.claim() is disabled above
})

// Add error handling for fetch events to prevent infinite loops
self.addEventListener('fetch', event => {
  // Skip non-GET requests to prevent issues with POST/PUT/DELETE
  if (event.request.method !== 'GET') {
    return
  }

  // Skip chrome-extension requests that can cause issues
  if (event.request.url.startsWith('chrome-extension://')) {
    return
  }

  // Skip requests with no-cache headers that might cause loops
  if (event.request.headers.get('cache-control') === 'no-cache') {
    console.log('Skipping no-cache request:', event.request.url)
    return
  }
})

serwist.addEventListeners()
