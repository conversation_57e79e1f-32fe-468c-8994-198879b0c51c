import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import useOnlineStatus from '../hooks/useOnlineStatus'

/**
 * OfflineBanner component that shows when the user is offline
 * Directs users to the downloads page where they can watch downloaded videos
 */
export default function OfflineBanner() {
  const isOnline = useOnlineStatus()
  const router = useRouter()
  const [showBackOnline, setShowBackOnline] = useState(false)
  const [wasOffline, setWasOffline] = useState(false)

  // Check if we're on the downloads page
  const isOnDownloadsPage = router.pathname === '/downloads'

  // Handle the "back online" notification
  useEffect(() => {
    if (!isOnline) {
      setWasOffline(true)
      setShowBackOnline(false)
    } else if (wasOffline && isOnline) {
      // User just came back online
      setShowBackOnline(true)
      // Hide the "back online" message after 3 seconds
      const timer = setTimeout(() => {
        setShowBackOnline(false)
        setWasOffline(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [isOnline, wasOffline])

  // Don't render anything if the user is online and we're not showing the "back online" message
  if (isOnline && !showBackOnline) {
    return null
  }

  // Determine banner styling and content based on online status
  const isShowingBackOnline = isOnline && showBackOnline
  const bannerClasses = isShowingBackOnline
    ? 'fixed top-0 left-0 right-0 z-[9999] bg-success-100 border-b border-success-200 text-success-800 px-4 py-3 shadow-sm'
    : 'fixed top-0 left-0 right-0 z-[9999] bg-warning-100 border-b border-warning-200 text-warning-800 px-4 py-3 shadow-sm'

  return (
    <>
      <div className={bannerClasses}>
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex items-center gap-2">
              {isShowingBackOnline ? (
                // Check icon for back online
                <svg
                  className="w-5 h-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                // Warning icon for offline
                <svg
                  className="w-5 h-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              <span className="text-sm sm:text-base font-medium">
                {isShowingBackOnline
                  ? "You're back online!"
                  : "You're currently offline"}
              </span>
            </div>

            {!isShowingBackOnline && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm">
                <span className="text-warning-700">
                  {isOnDownloadsPage
                    ? 'You can watch your downloaded videos below!'
                    : 'You can still watch your downloaded videos!'}
                </span>
                {!isOnDownloadsPage && (
                  <Link
                    href="/downloads"
                    className="inline-flex items-center px-3 py-1.5 bg-warning-200 hover:bg-warning-300 text-warning-900 rounded-md font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2"
                  >
                    Go to Downloads
                    <svg
                      className="w-4 h-4 ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Spacer to push content below the fixed banner */}
      <div className="h-16 sm:h-14" />
    </>
  )
}
